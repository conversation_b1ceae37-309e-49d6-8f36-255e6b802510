/**
 * Order modification page
 */

import { ConfirmationDialog } from '@/components/orders/ConfirmationDialog';
import { OrderDetailsCard } from '@/components/orders/OrderDetailsCard';
import { OrderItemsList } from '@/components/orders/OrderItemsList';
import { ThemedText } from '@/components/ThemedText';
import { ThemedView } from '@/components/ThemedView';
import { Button } from '@/components/ui/Button';
import { IconSymbol } from '@/components/ui/IconSymbol';
import { LoadingSpinner } from '@/components/ui/LoadingSpinner';
import { useNetworkStatus } from '@/hooks/useNetworkStatus';
import { useOrderManagement } from '@/hooks/useOrderManagement';
import { useThemeColor } from '@/hooks/useThemeColor';
import {
    ModificationRequest,
    OrderItemModification
} from '@/types/order';
import { formatCurrency, formatTimeRemaining, validateModifications } from '@/utils/orderUtils';
import { router, Stack, useLocalSearchParams } from 'expo-router';
import React, { useEffect, useMemo, useState } from 'react';
import {
    Alert,
    KeyboardAvoidingView,
    Platform,
    ScrollView,
    StyleSheet,
    View
} from 'react-native';

export default function OrderModificationPage() {
  const { id } = useLocalSearchParams<{ id: string }>();
  const { state, actions } = useOrderManagement();
  const { isOnline } = useNetworkStatus();

  const [modifications, setModifications] = useState<OrderItemModification[]>([]);
  const [showConfirmation, setShowConfirmation] = useState(false);
  const [isSubmitting, setIsSubmitting] = useState(false);

  const borderColor = useThemeColor({}, 'border');
  const cardBackground = useThemeColor({}, 'cardBackground');
  const mutedColor = useThemeColor({}, 'muted');
  const tintColor = useThemeColor({}, 'tint');
  const errorColor = useThemeColor({}, 'error');
  const warningColor = useThemeColor({}, 'warning');
  const successColor = useThemeColor({}, 'success');

  useEffect(() => {
    if (id) {
      actions.loadOrder(id);
    }
  }, [id, actions]);

  // Calculate modified items and pricing
  const { modifiedItems, newTotal, priceDifference } = useMemo(() => {
    if (!state.order) return { modifiedItems: new Set(), newTotal: 0, priceDifference: 0 };

    const modifiedItemIds = new Set(modifications.map(mod => mod.itemId));
    const newTotal = actions.calculateModificationPricing(modifications);
    const priceDifference = newTotal - state.order.pricing.total;

    return { modifiedItems: modifiedItemIds, newTotal, priceDifference };
  }, [modifications, state.order, actions]);

  // Validation
  const validationResult = useMemo(() => {
    if (!state.order) return { isValid: false, errors: [] };
    return validateModifications(state.order, modifications);
  }, [state.order, modifications]);

  const handleItemModification = (modification: OrderItemModification) => {
    setModifications(prev => {
      // Remove any existing modifications for this item
      const filtered = prev.filter(mod => mod.itemId !== modification.itemId);

      // Add the new modification
      return [...filtered, modification];
    });
  };

  const handleSaveChanges = () => {
    if (!validationResult.isValid) {
      Alert.alert('Invalid Modifications', validationResult.errors.join('\n'));
      return;
    }

    if (modifications.length === 0) {
      Alert.alert('No Changes', 'You haven\'t made any modifications to your order.');
      return;
    }

    setShowConfirmation(true);
  };

  const handleConfirmModification = async () => {
    if (!state.order || modifications.length === 0) return;

    setIsSubmitting(true);

    try {
      const request: ModificationRequest = {
        orderId: state.order.id,
        modifications,
        reason: 'Customer requested changes',
      };

      const result = await actions.modifyOrder(request);

      setShowConfirmation(false);

      Alert.alert(
        'Modification Successful',
        result.message,
        [
          {
            text: 'OK',
            onPress: () => router.back(),
          },
        ]
      );
    } catch (error) {
      setShowConfirmation(false);
      Alert.alert(
        'Modification Failed',
        error instanceof Error ? error.message : 'Failed to modify order. Please try again.',
        [{ text: 'OK' }]
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleResetChanges = () => {
    Alert.alert(
      'Reset Changes',
      'Are you sure you want to reset all modifications?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Reset',
          style: 'destructive',
          onPress: () => setModifications([])
        },
      ]
    );
  };

  if (state.loading) {
    return (
      <ThemedView style={styles.container}>
        <LoadingSpinner message="Loading order details..." />
      </ThemedView>
    );
  }

  if (state.error) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.errorContainer}>
          <IconSymbol name="exclamationmark.triangle" size={48} color={errorColor} />
          <ThemedText type="subtitle" style={styles.errorTitle}>
            Error Loading Order
          </ThemedText>
          <ThemedText style={[styles.errorMessage, { color: mutedColor }]}>
            {state.error}
          </ThemedText>
          <Button
            title="Try Again"
            onPress={() => id && actions.loadOrder(id)}
            style={styles.retryButton}
          />
        </View>
      </ThemedView>
    );
  }

  if (!state.order || !state.timeConstraints) {
    return (
      <ThemedView style={styles.container}>
        <View style={styles.errorContainer}>
          <IconSymbol name="questionmark.circle" size={48} color={mutedColor} />
          <ThemedText type="subtitle" style={styles.errorTitle}>
            Order Not Found
          </ThemedText>
          <ThemedText style={[styles.errorMessage, { color: mutedColor }]}>
            The order you&apos;re looking for could not be found.
          </ThemedText>
        </View>
      </ThemedView>
    );
  }

  const canModify = state.timeConstraints.canModify;
  const timeRemaining = state.timeConstraints.timeRemaining.modification;
  const hasChanges = modifications.length > 0;

  return (
    <>
      <Stack.Screen
        options={{
          title: 'Modify Order',
          headerTintColor: tintColor,
        }}
      />

      <KeyboardAvoidingView
        style={styles.container}
        behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      >
        <ScrollView
          style={styles.scrollView}
          contentContainerStyle={styles.scrollContent}
          showsVerticalScrollIndicator={false}
        >
          {/* Network Status Warning */}
          {!isOnline && (
            <View style={[styles.offlineWarning, { backgroundColor: `${warningColor}15`, borderColor: `${warningColor}40` }]}>
              <IconSymbol name="wifi.slash" size={16} color={warningColor} />
              <ThemedText style={[styles.offlineText, { color: warningColor }]}>
                You&apos;re offline. Modifications will be processed when connection is restored.
              </ThemedText>
            </View>
          )}

          {/* Order Details */}
          <OrderDetailsCard
            order={state.order}
            showTimeConstraints
            timeRemaining={state.timeConstraints.timeRemaining}
          />

          {/* Modification Status */}
          {!canModify ? (
            <View style={[styles.statusCard, { backgroundColor: `${errorColor}15`, borderColor: `${errorColor}40` }]}>
              <IconSymbol name="clock.badge.xmark" size={24} color={errorColor} />
              <View style={styles.statusContent}>
                <ThemedText type="defaultSemiBold" style={styles.statusTitle}>
                  Modification Not Available
                </ThemedText>
                <ThemedText style={[styles.statusMessage, { color: mutedColor }]}>
                  The modification window has expired. Contact customer support for assistance.
                </ThemedText>
              </View>
            </View>
          ) : (
            <>
              {/* Time Warning */}
              {timeRemaining <= 5 && (
                <View style={[styles.statusCard, { backgroundColor: `${warningColor}15`, borderColor: `${warningColor}40` }]}>
                  <IconSymbol name="clock" size={24} color={warningColor} />
                  <View style={styles.statusContent}>
                    <ThemedText type="defaultSemiBold" style={styles.statusTitle}>
                      Limited Time Remaining
                    </ThemedText>
                    <ThemedText style={[styles.statusMessage, { color: mutedColor }]}>
                      You have {formatTimeRemaining(timeRemaining)} left to modify this order.
                    </ThemedText>
                  </View>
                </View>
              )}

              {/* Modification Policy */}
              <View style={[styles.policyCard, { backgroundColor: cardBackground, borderColor }]}>
                <ThemedText type="defaultSemiBold" style={styles.policyTitle}>
                  Modification Policy
                </ThemedText>
                <View style={styles.policyDetails}>
                  <View style={styles.policyRow}>
                    <IconSymbol
                      name={state.order.modificationPolicy.allowAddItems ? "checkmark.circle" : "xmark.circle"}
                      size={16}
                      color={state.order.modificationPolicy.allowAddItems ? successColor : errorColor}
                    />
                    <ThemedText style={styles.policyText}>Add new items</ThemedText>
                  </View>
                  <View style={styles.policyRow}>
                    <IconSymbol
                      name={state.order.modificationPolicy.allowRemoveItems ? "checkmark.circle" : "xmark.circle"}
                      size={16}
                      color={state.order.modificationPolicy.allowRemoveItems ? successColor : errorColor}
                    />
                    <ThemedText style={styles.policyText}>Remove items</ThemedText>
                  </View>
                  <View style={styles.policyRow}>
                    <IconSymbol
                      name={state.order.modificationPolicy.allowQuantityChanges ? "checkmark.circle" : "xmark.circle"}
                      size={16}
                      color={state.order.modificationPolicy.allowQuantityChanges ? successColor : errorColor}
                    />
                    <ThemedText style={styles.policyText}>Change quantities</ThemedText>
                  </View>
                  <View style={styles.policyRow}>
                    <IconSymbol
                      name={state.order.modificationPolicy.allowCustomizationChanges ? "checkmark.circle" : "xmark.circle"}
                      size={16}
                      color={state.order.modificationPolicy.allowCustomizationChanges ? successColor : errorColor}
                    />
                    <ThemedText style={styles.policyText}>Modify customizations</ThemedText>
                  </View>
                </View>
              </View>

              {/* Order Items with Modification Controls */}
              <OrderItemsList
                items={state.order.items}
                editable
                onItemModify={handleItemModification}
                modifiedItems={modifiedItems}
              />

              {/* Price Summary */}
              {hasChanges && (
                <View style={[styles.priceCard, { backgroundColor: cardBackground, borderColor }]}>
                  <ThemedText type="defaultSemiBold" style={styles.priceTitle}>
                    Price Summary
                  </ThemedText>
                  <View style={styles.priceDetails}>
                    <View style={styles.priceRow}>
                      <ThemedText style={styles.priceLabel}>Original Total:</ThemedText>
                      <ThemedText style={styles.priceValue}>
                        {formatCurrency(state.order.pricing.total)}
                      </ThemedText>
                    </View>
                    <View style={styles.priceRow}>
                      <ThemedText style={styles.priceLabel}>New Total:</ThemedText>
                      <ThemedText type="defaultSemiBold" style={styles.priceValue}>
                        {formatCurrency(newTotal)}
                      </ThemedText>
                    </View>
                    <View style={[styles.priceRow, styles.priceDifference]}>
                      <ThemedText type="defaultSemiBold" style={styles.priceLabel}>
                        Difference:
                      </ThemedText>
                      <ThemedText
                        type="defaultSemiBold"
                        style={[
                          styles.priceValue,
                          { color: priceDifference >= 0 ? errorColor : successColor }
                        ]}
                      >
                        {priceDifference >= 0 ? '+' : ''}{formatCurrency(priceDifference)}
                      </ThemedText>
                    </View>
                  </View>
                </View>
              )}
            </>
          )}
        </ScrollView>

        {/* Action Buttons */}
        {canModify && (
          <View style={[styles.actionBar, { borderTopColor: borderColor }]}>
            <Button
              title="Reset"
              variant="outline"
              onPress={handleResetChanges}
              style={styles.actionButton}
              disabled={!hasChanges}
            />
            <Button
              title="Save Changes"
              variant="primary"
              onPress={handleSaveChanges}
              style={styles.actionButton}
              disabled={!hasChanges || !validationResult.isValid}
            />
          </View>
        )}
      </KeyboardAvoidingView>

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        visible={showConfirmation}
        type="modify"
        title="Confirm Modifications"
        message="Are you sure you want to apply these changes to your order?"
        confirmText="Apply Changes"
        cancelText="Cancel"
        onConfirm={handleConfirmModification}
        onCancel={() => setShowConfirmation(false)}
        loading={isSubmitting}
        details={{
          newTotal,
          processingTime: isOnline ? 'Immediately' : 'When connection is restored',
        }}
      />
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollView: {
    flex: 1,
  },
  scrollContent: {
    padding: 16,
    paddingBottom: 100,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 32,
  },
  errorTitle: {
    textAlign: 'center',
    marginTop: 16,
    marginBottom: 8,
  },
  errorMessage: {
    textAlign: 'center',
    lineHeight: 24,
    marginBottom: 24,
  },
  retryButton: {
    minWidth: 120,
  },
  offlineWarning: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 12,
    borderRadius: 8,
    borderWidth: 1,
    marginBottom: 16,
  },
  offlineText: {
    fontSize: 14,
    marginLeft: 8,
    flex: 1,
  },
  statusCard: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 12,
    borderWidth: 1,
    marginBottom: 16,
  },
  statusContent: {
    marginLeft: 12,
    flex: 1,
  },
  statusTitle: {
    fontSize: 16,
    marginBottom: 4,
  },
  statusMessage: {
    fontSize: 14,
    lineHeight: 20,
  },
  policyCard: {
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
    marginBottom: 16,
  },
  policyTitle: {
    fontSize: 16,
    marginBottom: 12,
  },
  policyDetails: {
    gap: 8,
  },
  policyRow: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  policyText: {
    fontSize: 14,
    marginLeft: 8,
  },
  priceCard: {
    borderRadius: 12,
    borderWidth: 1,
    padding: 16,
    marginBottom: 16,
  },
  priceTitle: {
    fontSize: 16,
    marginBottom: 12,
  },
  priceDetails: {
    gap: 8,
  },
  priceRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  priceDifference: {
    paddingTop: 8,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  priceLabel: {
    fontSize: 14,
    flex: 1,
  },
  priceValue: {
    fontSize: 14,
  },
  actionBar: {
    flexDirection: 'row',
    padding: 16,
    borderTopWidth: 1,
    gap: 12,
  },
  actionButton: {
    flex: 1,
  },
});
