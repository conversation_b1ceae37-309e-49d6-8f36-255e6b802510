# Order Management System

This document describes the comprehensive order management system implemented for the food app, featuring order cancellation and modification capabilities with robust error handling, offline support, and accessibility features.

## 🚀 Features

### Order Cancellation
- **Time-based restrictions** - Orders can only be cancelled within a specified time window
- **Comprehensive refund calculation** - Automatic calculation of refund amounts and fees
- **Multiple cancellation reasons** - Predefined reasons with custom notes support
- **Policy display** - Clear presentation of cancellation terms and conditions
- **Confirmation dialogs** - Multi-step confirmation to prevent accidental cancellations

### Order Modification
- **Item quantity changes** - Increase or decrease item quantities
- **Item removal** - Remove items from existing orders
- **Real-time pricing** - Live calculation of price differences
- **Policy validation** - Respect restaurant-specific modification policies
- **Visual feedback** - Clear indication of modified items
- **Inventory constraints** - Handle unavailable items gracefully

### Technical Features
- **Offline support** - Queue actions when offline, sync when online
- **Error handling** - Comprehensive error boundaries and retry mechanisms
- **Accessibility** - Full screen reader support and keyboard navigation
- **Responsive design** - Optimized for mobile and desktop
- **Loading states** - Proper loading indicators and feedback
- **Network awareness** - Adapt behavior based on connection status

## 📁 File Structure

```
app/orders/
├── _layout.tsx              # Orders stack navigation
├── cancel/[id].tsx          # Order cancellation page
└── modify/[id].tsx          # Order modification page

components/orders/
├── OrderDetailsCard.tsx     # Order information display
├── OrderItemsList.tsx       # Items list with modification controls
├── OrderStatusBadge.tsx     # Status indicator component
└── ConfirmationDialog.tsx   # Confirmation dialogs

types/
└── order.ts                 # Order type definitions

utils/
├── orderUtils.ts            # Order calculation utilities
├── orderApi.ts              # API client with retry logic
└── orderStorage.ts          # Offline storage utilities

hooks/
└── useOrderManagement.ts    # Order management hook
```

## 🎯 Usage

### Navigation
Access order management from the home screen demo buttons:
- **Cancel Order Demo** - `/orders/cancel/12345`
- **Modify Order Demo** - `/orders/modify/12345`

### Order Cancellation
```tsx
import { router } from 'expo-router';

// Navigate to cancellation page
router.push(`/orders/cancel/${orderId}`);
```

### Order Modification
```tsx
import { router } from 'expo-router';

// Navigate to modification page
router.push(`/orders/modify/${orderId}`);
```

### Using the Hook
```tsx
import { useOrderManagement } from '@/hooks/useOrderManagement';

function OrderComponent() {
  const { state, actions } = useOrderManagement();

  useEffect(() => {
    actions.loadOrder('12345');
  }, []);

  const handleCancel = async () => {
    try {
      const result = await actions.cancelOrder({
        orderId: '12345',
        reason: 'Changed my mind',
        additionalNotes: 'Found a better deal'
      });
      console.log('Cancellation result:', result);
    } catch (error) {
      console.error('Cancellation failed:', error);
    }
  };

  return (
    <View>
      {state.loading && <LoadingSpinner />}
      {state.error && <ErrorMessage error={state.error} />}
      {state.order && <OrderDetails order={state.order} />}
    </View>
  );
}
```

## 🔧 Configuration

### Order Policies
Configure cancellation and modification policies per order:

```tsx
const order: Order = {
  // ... other order properties
  cancellationPolicy: {
    allowedUntilMinutes: 15,    // 15 minutes after placement
    refundPercentage: 100,      // 100% refund
    cancellationFee: 0,         // No cancellation fee
    reasons: [
      'Changed my mind',
      'Ordered by mistake',
      'Found a better deal',
      'Emergency came up',
      'Other'
    ]
  },
  modificationPolicy: {
    allowedUntilMinutes: 10,    // 10 minutes after placement
    allowAddItems: true,
    allowRemoveItems: true,
    allowQuantityChanges: true,
    allowCustomizationChanges: false
  }
};
```

### API Configuration
Set your API base URL in environment variables:

```bash
EXPO_PUBLIC_API_URL=https://api.yourfoodapp.com
```

## 🎨 UI Components

### OrderDetailsCard
Displays comprehensive order information with time constraints:

```tsx
<OrderDetailsCard 
  order={order}
  showTimeConstraints
  timeRemaining={timeConstraints.timeRemaining}
/>
```

### OrderItemsList
Interactive items list with modification controls:

```tsx
<OrderItemsList 
  items={order.items}
  editable
  onItemModify={handleItemModification}
  modifiedItems={modifiedItemIds}
/>
```

### ConfirmationDialog
Customizable confirmation dialogs:

```tsx
<ConfirmationDialog
  visible={showConfirmation}
  type="cancel"
  title="Cancel Order?"
  message="Are you sure you want to cancel this order?"
  onConfirm={handleConfirm}
  onCancel={handleCancel}
  details={{
    refundAmount: 52.84,
    processingTime: '3-5 business days'
  }}
/>
```

## 🔄 Offline Support

The system provides robust offline functionality:

### Automatic Queueing
- Actions are automatically queued when offline
- Queued actions are processed when connection is restored
- Users receive clear feedback about offline status

### Cached Data
- Orders are cached for offline viewing
- Last 50 orders are stored locally
- Cache is automatically updated when online

### Sync Management
```tsx
import { getPendingActions, removePendingAction } from '@/utils/orderStorage';

// Process pending actions when online
const pendingActions = await getPendingActions();
for (const action of pendingActions) {
  try {
    await processAction(action);
    await removePendingAction(action.id);
  } catch (error) {
    console.error('Failed to process action:', error);
  }
}
```

## ♿ Accessibility

### Screen Reader Support
- All interactive elements have proper ARIA labels
- Semantic HTML structure for web compatibility
- Descriptive text for complex interactions

### Keyboard Navigation
- Full keyboard navigation support
- Logical tab order
- Escape key handling for modals

### Visual Accessibility
- High contrast color schemes
- Scalable text and UI elements
- Clear visual hierarchy

## 🧪 Testing

### Mock Data
The system includes comprehensive mock data for development:

```tsx
import { mockOrderData } from '@/utils/orderApi';

// Use mock data in development
const order = __DEV__ ? mockOrderData : await fetchOrder(orderId);
```

### Error Scenarios
Test various error conditions:
- Network failures
- API errors
- Invalid order states
- Time constraint violations

## 🚨 Error Handling

### Network Errors
- Automatic retry with exponential backoff
- Graceful degradation to cached data
- Clear user feedback about connection issues

### Validation Errors
- Real-time validation of modifications
- Clear error messages
- Prevention of invalid actions

### API Errors
- Structured error responses
- Retry logic for transient failures
- Fallback to offline mode

## 📱 Platform Support

### React Native
- iOS and Android native apps
- Platform-specific optimizations
- Native navigation patterns

### Web (PWA)
- Progressive Web App capabilities
- Responsive design
- Touch and mouse interactions

## 🔮 Future Enhancements

- **Real-time updates** - WebSocket integration for live order status
- **Push notifications** - Order status change notifications
- **Advanced modifications** - Add new items from restaurant menu
- **Bulk operations** - Modify multiple orders simultaneously
- **Analytics** - Track cancellation and modification patterns
